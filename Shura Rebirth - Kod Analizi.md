# Ta<PERSON>h girdileri

# 1.Hafta

## 1. <PERSON><PERSON><PERSON> (6/19/2025)
<PERSON>ugün s-m-i çalıştı. toplantıda hikaye belirlendi, github eğitimi yapıldı. 
- Zorluk için seçenekler sunuldu:
- [ ] Normal- G<PERSON><PERSON> Zorluk
- [ ] Klasik 3 zorluk (kolay - orta - zor)
- [ ] 2 farklı oyun modu
- [ ] Tek zorluk

- GUI en baştan yapılacak. sorunlardan biri oyun ismi çok yukarda kaldı
- Cutscene eklenecek
- Yeni mekanikler:
- [ ] Player için Atlama animasyonu entegre
- [ ] Asgore'dan ilham alınarak seçilen boss saldır<PERSON>s<PERSON> (renk<PERSON> gözler)

## 2. gün
Karakterin iyleştirme mekaniklerini iyleştirme ve eklemeler yapıldı.

Karakterin 3 iyleştirme hakkı var. Ayrıca iyleştirme sırasında artık herhangi bir aksiyon alamaz hale geldi.

>[!info]  if not player.is_healing: player.move(keys, clicks)

Başarılı boss saldırılarında impact frame eklemesi yapıldı. Bunu gerçekleştiren metodun ayrıntıları functions.py 'da bulabilirsiniz.

## 3. gün
menu.py , firstGame.py ve boss.py dosyaları optimize edildi. Spagetti kodlar çıkarıldı ve kod daha temiz hale getirildi.

## 4. gün
Ana menüdeki çözünürlük ayarı değiştirildi. firstGame.py de while run döngüsünde büyük değişikliklere ve optimizasyonlara gidildi. Yaklaşık 100 satır kod silindi. 243.satırdan itibaren kod daha optimize durumda.

Çözünürlüğün tamamen bitmesi için 5 şeyin eklenmezi gerekiyor:

- [ ] Karakterlerin hitboxlarının büyüklüğü çözünürlüğe göre değişmeli
- [ ] Karakterlerin hurtboxlarının büyüklüğü çözünürlüğe göre değişmeli
- [ ] Karakterlerin spritelarının büyüklüğü çözünürlüğe göre değişmeli
- [ ] Karakterlerin konumları çözünürlüğe göre değişmeli
- [ ] Karakterlerin hızları çözünürlüğe göre değişmeli
## 5. gün

## 6. gün
>Karakter iyileştirme yeteneği kullanırken dash atabiliyor ve hareket edebiliyor!
- [x] Resume butonu
- [x] Main Menu butonu
- [ ] Boss hitbox - Try Again veya Retry yapıldığında başlangıç konumlarının iyileştirilmesi
- [ ] Boss hitbox - sprite hizası
## 7. gün
# 2.Hafta

## 1. gün

## 2. gün
## 3. gün
## 4. gün
## 5. gün

## 6. gün
## 7. gün

# 3.Hafta

## 1. gün

## 2. gün
## 3. gün
## 4. gün
## 5. gün

## 6. gün
## 7. gün

# 4.Hafta

## 1. gün

## 2. gün
## 3. gün
## 4. gün
## 5. gün

## 6. gün
## 7. gün

# 5.Hafta

## 1. gün

## 2. gün
## 3. gün
## 4. gün
## 5. gün

## 6. gün
## 7. gün

# 6.Hafta

## 1. gün

## 2. gün
## 3. gün
## 4. gün
## 5. gün

## 6. gün
## 7. gün